import { NextRequest } from "next/server";
import { getUsers } from "@/models/user";
import { getUserInfo } from "@/services/user";
import { getBatchUserCredits } from "@/services/credit";
import { respData, respErr } from "@/lib/resp";

export async function GET(req: NextRequest) {
  try {
    // 验证管理员权限
    const userInfo = await getUserInfo();
    if (!userInfo || !userInfo.email) {
      return respErr("Unauthorized");
    }

    const adminEmails = process.env.ADMIN_EMAILS?.split(",");
    if (!adminEmails?.includes(userInfo.email)) {
      return respErr("Access denied");
    }

    // 获取查询参数
    const { searchParams } = new URL(req.url);
    const email = searchParams.get("email");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "50");

    // 获取用户列表
    const users = await getUsers(page, limit, email || undefined);

    if (!users || users.length === 0) {
      return respData({
        users: [],
        page,
        limit,
        searchEmail: email,
      });
    }

    // 批量获取所有用户的积分信息，优化N+1查询问题
    const userUuids = users.map(user => user.uuid || "").filter(uuid => uuid);
    const batchCredits = await getBatchUserCredits(userUuids);

    // 组合用户信息和积分信息
    const usersWithCredits = users.map(user => {
      const userCredits = batchCredits.get(user.uuid || "") || { left_credits: 0 };
      return {
        ...user,
        credits: userCredits,
      };
    });

    return respData({
      users: usersWithCredits,
      page,
      limit,
      searchEmail: email,
    });
  } catch (error) {
    return respErr("Failed to fetch users");
  }
}
