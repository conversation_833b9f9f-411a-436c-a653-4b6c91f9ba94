import { refundCredits } from "@/services/credit";
import { getUserUuid } from "@/services/user";
import { respData, respErr } from "@/lib/resp";

/**
 * POST /api/user/credits/refund
 * Refund credits to user account (used for failed operations)
 */
export async function POST(req: Request) {
  try {
    // Get current user UUID
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("User not authenticated", 401);
    }

    // Parse request body
    const { 
      credits, 
      originalTransactionId,
      reason,
      metadata 
    } = await req.json();

    // Validate input parameters
    if (typeof credits !== 'number' || credits <= 0) {
      return respErr("Invalid credits amount", 400);
    }

    if (!originalTransactionId || typeof originalTransactionId !== 'string') {
      return respErr("Original transaction ID is required", 400);
    }

    // Perform credit refund
    const refundResult = await refundCredits({
      user_uuid,
      credits,
      originalTransactionId,
      reason: reason || "Operation failed - credits refunded",
    });

    // Return success response with refund details
    return respData({
      success: true,
      refundTransactionId: refundResult.refundTransactionId,
      refundedCredits: credits,
      originalTransactionId,
      reason: reason || "Operation failed - credits refunded",
      metadata: metadata || {},
      message: "Credits refunded successfully"
    });

  } catch (error) {
    // 根据错误类型返回不同的错误信息
    let errorMessage = "积分退款失败，请联系客服处理";
    let errorCode = 500;

    if (error instanceof Error) {
      const errorMsg = error.message.toLowerCase();

      if (errorMsg.includes('transaction') && errorMsg.includes('not found')) {
        errorMessage = "原始交易记录未找到，无法退款";
        errorCode = 404;
      } else if (errorMsg.includes('already refunded')) {
        errorMessage = "该交易已经退款，无法重复退款";
        errorCode = 400;
      } else if (errorMsg.includes('network') || errorMsg.includes('connection')) {
        errorMessage = "网络连接出现问题，请稍后重试";
        errorCode = 503;
      } else if (errorMsg.includes('timeout')) {
        errorMessage = "请求超时，请稍后重试";
        errorCode = 408;
      } else if (errorMsg.includes('database') || errorMsg.includes('db')) {
        errorMessage = "数据处理出现问题，请联系客服处理";
        errorCode = 500;
      }
    }

    return respErr(errorMessage, errorCode, {
      error: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
      retryable: errorCode === 503 || errorCode === 408, // 只有网络和超时错误可重试
    });
  }
}
