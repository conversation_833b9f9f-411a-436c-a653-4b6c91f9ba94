import { getUserCredits, checkSufficientCredits } from "@/services/credit";
import { getUserUuid } from "@/services/user";
import { respData, respErr } from "@/lib/resp";

/**
 * GET /api/user/credits
 * Get current user's credit balance
 */
export async function GET() {
  try {
    // Get current user UUID
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("User not authenticated", 401);
    }

    // Get user credits
    const userCredits = await getUserCredits(user_uuid);

    return respData({
      credits: userCredits,
      success: true,
    });
  } catch (error) {
    return respErr("Failed to get user credits", 500);
  }
}

/**
 * POST /api/user/credits
 * Check if user has sufficient credits for a specific operation
 */
export async function POST(req: Request) {
  try {
    // Get current user UUID
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("User not authenticated", 401);
    }

    // Parse request body
    const { requiredCredits } = await req.json();
    if (typeof requiredCredits !== 'number' || requiredCredits <= 0) {
      return respErr("Invalid required credits", 400);
    }

    // Check sufficient credits
    const result = await checkSufficientCredits(user_uuid, requiredCredits);

    // 正常的积分检查逻辑

    return respData({
      sufficient: result.sufficient,
      currentCredits: result.currentCredits,
      requiredCredits,
      success: true,
    });
  } catch (error) {
    // 根据错误类型返回不同的错误信息
    let errorMessage = "获取积分信息失败，请稍后重试";
    let errorCode = 500;

    if (error instanceof Error) {
      const errorMsg = error.message.toLowerCase();

      if (errorMsg.includes('network') || errorMsg.includes('connection')) {
        errorMessage = "网络连接出现问题，请检查网络后重试";
        errorCode = 503;
      } else if (errorMsg.includes('timeout')) {
        errorMessage = "请求超时，请稍后重试";
        errorCode = 408;
      } else if (errorMsg.includes('database') || errorMsg.includes('db')) {
        errorMessage = "数据处理出现问题，请稍后重试";
        errorCode = 500;
      }
    }

    return respErr(errorMessage, errorCode, {
      error: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
      retryable: true,
    });
  }
}
