# AI生成器设置文档

## 概述

本文档详细介绍了AI卡通生成器项目中所有与设置相关的内容，包括AI生成器的各种参数配置、翻译设置、系统配置等。

## 目录

1. [AI生成器核心设置](#ai生成器核心设置)
2. [样式选项设置](#样式选项设置)
3. [颜色选项设置](#颜色选项设置)
4. [光影选项设置](#光影选项设置)
5. [构图选项设置](#构图选项设置)
6. [系统环境配置](#系统环境配置)
7. [积分系统设置](#积分系统设置)
8. [API配置设置](#api配置设置)

## AI生成器核心设置

### 基础设置接口

```typescript
export interface AIGeneratorSettings {
  model?: string;
  style?: string;
  size?: string;
  quality?: string;
  steps?: number;
}
```

### 生成设置

```typescript
interface ControlsSectionProps {
  mode: "text-to-image" | "image-to-image";
  settings: {
    ratio: string;           // 图片比例
    style: string;           // 风格样式
    color: string;           // 色彩设置
    lighting: string;        // 光影设置
    composition: string;     // 构图设置
    imageCount: string;      // 生成数量
  };
  enableHighQuality: boolean;        // 高质量开关
  imageEnableHighQuality: boolean;   // 图生图高质量开关
}
```

### 比例设置选项

支持的图片比例：
- `1:1` - 正方形
- `16:9` - 宽屏横向
- `9:16` - 竖屏纵向
- `4:3` - 标准横向
- `3:4` - 标准纵向
- `2:3` - 肖像比例
- `3:2` - 风景比例

### 图片数量设置

```json
[
  { "zh": "1", "en": "1", "icon": "Square" },
  { "zh": "2", "en": "2", "icon": "Grid" },
  { "zh": "3", "en": "3", "icon": "Grid3X3" },
  { "zh": "4", "en": "4", "icon": "Layers" }
]
```

## 样式选项设置

### 样式选项列表

| 中文名称 | 英文名称 | 参考作品 | 图标 |
|---------|---------|---------|------|
| 无风格 | No Style | - | Ban |
| 写实风 | Semi-Realistic | Arcane (Netflix) | Camera |
| 极简扁平 | Flat Minimal | Adventure Time | Square |
| 超级英雄风 | Comic Book | Batman: The Animated Series | Zap |
| 青年热血动漫风 | Shounen Style | My Hero Academia | Flame |
| 可爱风 | Moe Style | K-On! | Heart |
| 暗黑成人风 | Gritty Style | Castlevania | Moon |
| 哥特风 | Gothic Style | Billy & Mandy | Crown |
| 脱线搞怪风 | Absurdist | Rick and Morty | Smile |
| 手绘感风 | Sketchy Lines | The Midnight Gospel | Pencil |
| 二头身Q版 | Chibi Style | Lucky Star | Circle |
| 超现实风 | Surreal Style | FLCL | Sparkles |
| 黑白漫画风 | Monochrome | Samurai Jack | Contrast |
| 美式卡通 | Classic Western | Tom and Jerry | Star |
| 高饱和亮色 | Vibrant Colors | Gumball | Rainbow |
| 2.5D分层 | Cutout Style | South Park | Layers |
| 青春柔光风 | Soft Pastel | Your Name | Sun |
| 抽象表现风 | Experimental | Puparia | Shapes |
| 动态动感风 | Kinetic Style | Attack on Titan | Wind |
| 视觉小说风 | Visual Novel | Steins;Gate | Book |
| 复古赛璐璐手绘风 | Retro Cel | Cowboy Bebop | Clock |

## 颜色选项设置

### 颜色选项列表

| 中文名称 | 英文名称 | 参考作品 | 图标 |
|---------|---------|---------|------|
| 无色彩 | No Color | - | Ban |
| 高饱和鲜艳色 | Vibrant Colors | Kill la Kill | Palette |
| 柔和淡色系 | Pastel Tones | Your Name | Flower |
| 低饱和色 | Muted Colors | Death Note | Moon |
| 暗黑风格色调 | Dark Palette | Tokyo Ghoul | Skull |
| 复古色彩 | Retro Colors | Cowboy Bebop | Clock |
| 冷色主导 | Cool Tones | Serial Experiments Lain | Snowflake |
| 暖色主导 | Warm Tones | Clannad | Sun |
| 单色调 | Monochrome | Samurai Jack | Circle |
| 高对比强明暗 | High Contrast | Attack on Titan | Contrast |
| 赛博霓虹色 | Cyber Neon | Akira | Zap |
| 梦幻透明感色系 | Dreamy Glow | The Garden of Words | Sparkles |
| 自然写实色调 | Natural Colors | Spirited Away | Mountain |

## 光影选项设置

### 光影选项列表

| 中文名称 | 英文名称 | 参考作品 | 图标 |
|---------|---------|---------|------|
| 无光影 | No Light | - | Ban |
| 柔光 | Soft Lighting | Your Name | Sun |
| 高对比光 | High Contrast Lighting | Batman: TAS | Contrast |
| 逆光 | Backlighting | Violet Evergarden | Sunrise |
| 顶光 | Top Lighting | Death Note | ArrowUp |
| 底光 | Under Lighting | Mononoke | ArrowDown |
| 轮廓光 | Rim Lighting | Fate/stay night: UBW | Circle |
| 环境光 | Ambient Lighting | Spirited Away | Globe |
| 聚焦高亮 | Spot Lighting | Mob Psycho 100 | Flashlight |
| 光斑 | Light Leaks / Lens Flare | The Garden of Words | Sparkles |
| 赛博霓虹光 | Neon Lighting | Cyberpunk: Edgerunners | Zap |
| 黄金时段光 | Golden Hour Lighting | 5 Centimeters per Second | Sunset |
| 月光 | Moonlight / Cool Light | Princess Mononoke | Moon |

## 构图选项设置

### 构图选项列表

| 中文名称 | 英文名称 | 参考作品 | 图标 |
|---------|---------|---------|------|
| 无构图 | No Composition | - | Ban |
| 三分法 | Rule of Thirds | Spirited Away | Grid3X3 |
| 中心构图 | Centered Composition | Demon Slayer | Target |
| 对称构图 | Symmetrical Composition | Violet Evergarden | FlipHorizontal |
| 动态构图 | Dynamic Composition | Promare | Wind |
| 引导线构图 | Leading Lines | Akira | ArrowRight |
| 负空间构图 | Negative Space | 5 Centimeters per Second | Square |
| 框架内构图 | Frame Within a Frame | Paranoia Agent | Frame |
| 鸟瞰角度 | Bird's Eye View | Attack on Titan | Eye |
| 低角度视角 | Low Angle Shot | Code Geass | ArrowUp |
| 第一人称视角 | POV Composition | Re:Zero | User |
| 极端特写 | Extreme Close-Up | Death Note | ZoomIn |
| 对角线构图 | Diagonal Composition | FLCL | Slash |
| 黄金比例构图 | Golden Ratio Composition | Princess Mononoke | RotateCcw |
| 镜像构图 | Reflection Composition | Paprika | FlipVertical |

## 系统环境配置

### 环境变量设置

#### 基础Web信息
```bash
# Web基础信息
NEXT_PUBLIC_WEB_URL = "http://localhost:3000"
NEXT_PUBLIC_PROJECT_NAME = "ShipAny"
```

#### 数据库配置 (Supabase)
```bash
# Supabase数据库配置
SUPABASE_URL = ""
SUPABASE_ANON_KEY = ""
SUPABASE_SERVICE_ROLE_KEY = ""
```

#### 身份验证配置
```bash
# Auth密钥
AUTH_SECRET = "Zt3BXVudzzRq2R2WBqhwRy1dNMq48Gg9zKAYq7YwSL0="

# Google OAuth
AUTH_GOOGLE_ID = ""
AUTH_GOOGLE_SECRET = ""
NEXT_PUBLIC_AUTH_GOOGLE_ID = ""
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED = "false"
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED = "false"

# GitHub OAuth
AUTH_GITHUB_ID = ""
AUTH_GITHUB_SECRET = ""
NEXT_PUBLIC_AUTH_GITHUB_ENABLED = "false"
```

#### 支付配置 (Stripe)
```bash
# Stripe支付配置
STRIPE_PUBLIC_KEY = ""
STRIPE_PRIVATE_KEY = ""
STRIPE_WEBHOOK_SECRET = ""

# 支付回调URL
NEXT_PUBLIC_PAY_SUCCESS_URL = "http://localhost:3000/my-orders"
NEXT_PUBLIC_PAY_FAIL_URL = "http://localhost:3000/#pricing"
NEXT_PUBLIC_PAY_CANCEL_URL = "http://localhost:3000/#pricing"
```

#### 存储配置 (AWS S3)
```bash
# 存储配置
STORAGE_ENDPOINT = ""
STORAGE_REGION = ""
STORAGE_ACCESS_KEY = ""
STORAGE_SECRET_KEY = ""
STORAGE_BUCKET = ""
STORAGE_DOMAIN = ""
```

#### 分析配置
```bash
# Google Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID = ""

# OpenPanel Analytics
NEXT_PUBLIC_OPENPANEL_CLIENT_ID = ""
```

#### 其他配置
```bash
# 语言检测
NEXT_PUBLIC_LOCALE_DETECTION = "false"

# 管理员邮箱
ADMIN_EMAILS = ""

# 默认主题
NEXT_PUBLIC_DEFAULT_THEME = "light"
```

### AI服务提供商配置

#### Kling AI配置
```bash
# Kling AI API配置
KLING_ACCESS_KEY = ""
KLING_SECRET_KEY = ""
```

#### OpenAI配置
```bash
# OpenAI API配置
OPENAI_API_KEY = ""
```

#### Replicate配置
```bash
# Replicate API配置
REPLICATE_API_TOKEN = ""
```

## 积分系统设置

### 积分数量配置

```typescript
export enum CreditsAmount {
  NewUserGet = 10,              // 新用户获得积分
  PingCost = 1,                 // Ping操作消耗
  ImageGenerationBase = 1,      // 图片生成基础消耗
}
```

### 生成设置接口

```typescript
export interface GenerationSettings {
  imageCount: number;           // 生成图片数量
  highQuality: boolean;         // 是否高质量
}
```

### 积分计算结果

```typescript
export interface CreditCalculation {
  totalCredits: number;         // 总积分消耗
  baseCredits: number;          // 基础积分
  qualityMultiplier: number;    // 质量倍数
  imageCount: number;           // 图片数量
}
```

### 积分缓存配置

```typescript
const CACHE_TTL = 30 * 1000;           // 30秒缓存
const ACTIVE_USER_CACHE_TTL = 10 * 1000; // 活跃用户10秒缓存
```

## API配置设置

### 图片生成API配置

#### OpenAI配置
```typescript
case "openai":
  imageModel = openai.image(model);
  providerOptions = {
    openai: {
      quality: highQuality ? "hd" : "standard",
      style: "natural",
    },
  };
  break;
```

#### Replicate配置
```typescript
case "replicate":
  imageModel = replicate.image(model);
  providerOptions = {
    replicate: {
      output_quality: highQuality ? 95 : 80,
    },
  };
  break;
```

#### Kling配置
```typescript
case "kling":
  imageModel = kling.image(model);
  providerOptions = {
    kling: {},
  };
  break;
```

### API测试配置

```http
@baseUrl = http://localhost:3000
@apiKey = sk-xxx

### 图片生成测试
POST {{baseUrl}}/api/demo/gen-image
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
  "prompt": "a beautiful girl running with 2 cats",
  "provider": "replicate",
  "model": "black-forest-labs/flux-schnell"
}
```

## 国际化设置

### 支持的语言

```typescript
export const locales = ["en", "zh"];

export const localeNames: any = {
  en: "English",
  zh: "中文",
};

export const defaultLocale = "en";
```

### 语言检测配置

```typescript
export const localeDetection =
  process.env.NEXT_PUBLIC_LOCALE_DETECTION === "true";
```

### 路径名配置

```typescript
export const pathnames = {
  en: {
    "privacy-policy": "/privacy-policy",
    "terms-of-service": "/terms-of-service",
  },
} satisfies Pathnames<typeof locales>;
```

## 翻译设置

### AI生成器翻译接口

```typescript
export interface AIGeneratorTranslations {
  tabs?: {
    text_to_image?: string;
    image_to_image?: string;
  };
  placeholders?: {
    negative_prompt?: string;
    image_modification?: string;
    file_support?: string;
  };
  settings?: {
    ratio?: string;
    style?: string;
    color?: string;
    lighting?: string;
    composition?: string;
  };
  controls?: {
    high_quality?: string;
    generating?: string;
    generate?: string;
  };
}
```

### 控制按钮翻译

#### 中文翻译
```json
{
  "controls": {
    "high_quality": "高质量",
    "generating": "生成中...",
    "generate": "生成图片"
  }
}
```

#### 英文翻译
```json
{
  "controls": {
    "high_quality": "High Quality",
    "generating": "Generating...",
    "generate": "Generate Image"
  }
}
```

### 占位符翻译

#### 中文占位符
```json
{
  "placeholders": {
    "negative_prompt": "描述你不想要在图片中出现的内容...",
    "image_modification": "描述你想要对图片进行的修改...",
    "file_support": "支持 JPG, PNG, WebP"
  }
}
```

#### 英文占位符
```json
{
  "placeholders": {
    "negative_prompt": "Describe what you don't want in the image...",
    "image_modification": "Describe the modifications you want to make to the image...",
    "file_support": "Supports JPG, PNG, WebP"
  }
}
```
