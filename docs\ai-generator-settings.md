# AI Generator Settings Documentation

## Overview

This document provides comprehensive documentation for all AI generator settings and configurations in the AI Cartoon Generator project.

## Table of Contents

1. [Core AI Generator Settings](#core-ai-generator-settings)
2. [Style Options](#style-options)
3. [Color Options](#color-options)
4. [Lighting Options](#lighting-options)
5. [Composition Options](#composition-options)
6. [Image Generation Settings](#image-generation-settings)
7. [Translation Settings](#translation-settings)

## Core AI Generator Settings

### Basic Settings Interface

```typescript
export interface AIGeneratorSettings {
  model?: string;
  style?: string;
  size?: string;
  quality?: string;
  steps?: number;
}
```

### Controls Section Props

```typescript
interface ControlsSectionProps {
  mode: "text-to-image" | "image-to-image";
  settings: {
    ratio: string;           // Image aspect ratio
    style: string;           // Art style
    color: string;           // Color scheme
    lighting: string;        // Lighting setup
    composition: string;     // Composition technique
    imageCount: string;      // Number of images to generate
  };
  enableHighQuality: boolean;        // High quality toggle for text-to-image
  imageEnableHighQuality: boolean;   // High quality toggle for image-to-image
}
```

### Aspect Ratio Options

Supported image aspect ratios:
- `1:1` - Square format
- `16:9` - Widescreen landscape
- `9:16` - Portrait vertical
- `4:3` - Standard landscape
- `3:4` - Standard portrait
- `2:3` - Portrait ratio
- `3:2` - Landscape ratio

### Image Count Options

```json
[
  { "zh": "1", "en": "1", "icon": "Square" },
  { "zh": "2", "en": "2", "icon": "Grid" },
  { "zh": "3", "en": "3", "icon": "Grid3X3" },
  { "zh": "4", "en": "4", "icon": "Layers" }
]
```

## Style Options

### Available Art Styles

| English Name | Reference Work | Icon |
|-------------|----------------|------|
| No Style | - | Ban |
| Semi-Realistic | Arcane (Netflix) | Camera |
| Flat Minimal | Adventure Time | Square |
| Comic Book | Batman: The Animated Series | Zap |
| Shounen Style | My Hero Academia | Flame |
| Moe Style | K-On! | Heart |
| Gritty Style | Castlevania | Moon |
| Gothic Style | Billy & Mandy | Crown |
| Absurdist | Rick and Morty | Smile |
| Sketchy Lines | The Midnight Gospel | Pencil |
| Chibi Style | Lucky Star | Circle |
| Surreal Style | FLCL | Sparkles |
| Monochrome | Samurai Jack | Contrast |
| Classic Western | Tom and Jerry | Star |
| Vibrant Colors | Gumball | Rainbow |
| Cutout Style | South Park | Layers |
| Soft Pastel | Your Name | Sun |
| Experimental | Puparia | Shapes |
| Kinetic Style | Attack on Titan | Wind |
| Visual Novel | Steins;Gate | Book |
| Retro Cel | Cowboy Bebop | Clock |

### Style Option Interface

```typescript
export interface StyleOption {
  zh: string;        // Chinese name
  en: string;        // English name
  reference: string; // Reference artwork
  icon: string;      // Icon identifier
}
```

## Color Options

### Available Color Schemes

| English Name | Reference Work | Icon |
|-------------|----------------|------|
| No Color | - | Ban |
| Vibrant Colors | Kill la Kill | Palette |
| Pastel Tones | Your Name | Flower |
| Muted Colors | Death Note | Moon |
| Dark Palette | Tokyo Ghoul | Skull |
| Retro Colors | Cowboy Bebop | Clock |
| Cool Tones | Serial Experiments Lain | Snowflake |
| Warm Tones | Clannad | Sun |
| Monochrome | Samurai Jack | Circle |
| High Contrast | Attack on Titan | Contrast |
| Cyber Neon | Akira | Zap |
| Dreamy Glow | The Garden of Words | Sparkles |
| Natural Colors | Spirited Away | Mountain |

### Color Option Interface

```typescript
export interface ColorOption {
  zh: string;        // Chinese name
  en: string;        // English name
  reference: string; // Reference artwork
  icon: string;      // Icon identifier
}
```

## Lighting Options

### Available Lighting Setups

| English Name | Reference Work | Icon |
|-------------|----------------|------|
| No Light | - | Ban |
| Soft Lighting | Your Name | Sun |
| High Contrast Lighting | Batman: TAS | Contrast |
| Backlighting | Violet Evergarden | Sunrise |
| Top Lighting | Death Note | ArrowUp |
| Under Lighting | Mononoke | ArrowDown |
| Rim Lighting | Fate/stay night: UBW | Circle |
| Ambient Lighting | Spirited Away | Globe |
| Spot Lighting | Mob Psycho 100 | Flashlight |
| Light Leaks / Lens Flare | The Garden of Words | Sparkles |
| Neon Lighting | Cyberpunk: Edgerunners | Zap |
| Golden Hour Lighting | 5 Centimeters per Second | Sunset |
| Moonlight / Cool Light | Princess Mononoke | Moon |

### Lighting Option Interface

```typescript
export interface LightingOption {
  zh: string;        // Chinese name
  en: string;        // English name
  reference: string; // Reference artwork
  icon: string;      // Icon identifier
}
```

## Composition Options

### Available Composition Techniques

| English Name | Reference Work | Icon |
|-------------|----------------|------|
| No Composition | - | Ban |
| Rule of Thirds | Spirited Away | Grid3X3 |
| Centered Composition | Demon Slayer | Target |
| Symmetrical Composition | Violet Evergarden | FlipHorizontal |
| Dynamic Composition | Promare | Wind |
| Leading Lines | Akira | ArrowRight |
| Negative Space | 5 Centimeters per Second | Square |
| Frame Within a Frame | Paranoia Agent | Frame |
| Bird's Eye View | Attack on Titan | Eye |
| Low Angle Shot | Code Geass | ArrowUp |
| POV Composition | Re:Zero | User |
| Extreme Close-Up | Death Note | ZoomIn |
| Diagonal Composition | FLCL | Slash |
| Golden Ratio Composition | Princess Mononoke | RotateCcw |
| Reflection Composition | Paprika | FlipVertical |

### Composition Option Interface

```typescript
export interface CompositionOption {
  zh: string;        // Chinese name
  en: string;        // English name
  reference: string; // Reference artwork
  icon: string;      // Icon identifier
}
```

## Image Generation Settings

### Generation Settings Interface

```typescript
export interface GenerationSettings {
  ratio: string;
  style: string;
  color: string;
  lighting: string;
  composition: string;
  imageCount: string;
  enableHighQuality: boolean;
  provider: string;          // AI service provider (openai, replicate, kling)
  model: string;             // Specific model to use
}
```

### Provider Configuration

#### OpenAI Configuration
```typescript
case "openai":
  imageModel = openai.image(model);
  providerOptions = {
    openai: {
      quality: highQuality ? "hd" : "standard",
      style: "natural",
    },
  };
  break;
```

#### Replicate Configuration
```typescript
case "replicate":
  imageModel = replicate.image(model);
  providerOptions = {
    replicate: {
      output_quality: highQuality ? 95 : 80,
    },
  };
  break;
```

#### Kling Configuration
```typescript
case "kling":
  imageModel = kling.image(model);
  providerOptions = {
    kling: {},
  };
  break;
```

### Default Settings

```typescript
const [settings, setSettings] = useState({
  ratio: settingsOptions.ratio[0],                    // "1:1"
  style: getOptionDisplayText(settingsOptions.style[0], getCurrentLocale()),
  color: getOptionDisplayText(settingsOptions.color[0], getCurrentLocale()),
  lighting: getOptionDisplayText(settingsOptions.lighting[0], getCurrentLocale()),
  composition: getOptionDisplayText(settingsOptions.composition[0], getCurrentLocale()),
  imageCount: getOptionDisplayText(settingsOptions.imageCount[0], getCurrentLocale())
});
```

## Translation Settings

### AI Generator Translations Interface

```typescript
export interface AIGeneratorTranslations {
  tabs?: {
    text_to_image?: string;
    image_to_image?: string;
  };
  placeholders?: {
    negative_prompt?: string;
    image_modification?: string;
    file_support?: string;
  };
  settings?: {
    ratio?: string;
    style?: string;
    color?: string;
    lighting?: string;
    composition?: string;
  };
  controls?: {
    high_quality?: string;
    generating?: string;
    generate?: string;
  };
}
```

### Tab Labels

#### English
```json
{
  "tabs": {
    "text_to_image": "Text to Image",
    "image_to_image": "Image to Image"
  }
}
```

#### Chinese
```json
{
  "tabs": {
    "text_to_image": "文生图",
    "image_to_image": "图生图"
  }
}
```

### Placeholder Text

#### English
```json
{
  "placeholders": {
    "negative_prompt": "Describe what you don't want in the image...",
    "image_modification": "Describe the modifications you want to make to the image...",
    "file_support": "Supports JPG, PNG, WebP"
  }
}
```

#### Chinese
```json
{
  "placeholders": {
    "negative_prompt": "描述你不想要在图片中出现的内容...",
    "image_modification": "描述你想要对图片进行的修改...",
    "file_support": "支持 JPG, PNG, WebP"
  }
}
```

### Control Labels

#### English
```json
{
  "controls": {
    "high_quality": "High Quality",
    "generating": "Generating...",
    "generate": "Generate Image"
  }
}
```

#### Chinese
```json
{
  "controls": {
    "high_quality": "高质量",
    "generating": "生成中...",
    "generate": "生成图片"
  }
}
```

### Settings Labels

#### English
```json
{
  "settings": {
    "ratio": "Ratio",
    "style": "Style",
    "color": "Color",
    "lighting": "Lighting",
    "composition": "Composition",
    "imageCount": "Count",
    "imageCount_unit": " Image(s)"
  }
}
```

#### Chinese
```json
{
  "settings": {
    "ratio": "比例",
    "style": "风格",
    "color": "色彩",
    "lighting": "光影",
    "composition": "构图",
    "imageCount": "数量",
    "imageCount_unit": "张"
  }
}
```
