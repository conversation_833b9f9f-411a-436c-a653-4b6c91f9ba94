# 积分系统配置文档

## 概述

本文档详细说明了AI卡通生成器项目中积分系统的所有配置和设置。

## 积分系统核心配置

### 积分数量枚举

```typescript
export enum CreditsAmount {
  NewUserGet = 10,              // 新用户注册获得积分
  PingCost = 1,                 // Ping操作消耗积分
  ImageGenerationBase = 1,      // 图片生成基础消耗积分
}
```

### 生成设置接口

```typescript
export interface GenerationSettings {
  imageCount: number;           // 生成图片数量
  highQuality: boolean;         // 是否启用高质量模式
}
```

### 积分计算结果接口

```typescript
export interface CreditCalculation {
  totalCredits: number;         // 总积分消耗
  baseCredits: number;          // 基础积分消耗
  qualityMultiplier: number;    // 质量倍数
  imageCount: number;           // 图片数量
}
```

### 用户积分接口

```typescript
export interface UserCredits {
  totalCredits: number;         // 总可用积分
  expiredCredits: number;       // 即将过期积分
  transactions: CreditTransaction[]; // 交易记录
}

export interface CreditTransaction {
  id: string;
  trans_no: string;
  created_at: string;
  user_uuid: string;
  trans_type: string;
  credits: number;
  order_no?: string;
  expired_at?: string;
}
```

## 积分缓存配置

### 缓存设置

```typescript
// 缓存时间配置
const CACHE_TTL = 30 * 1000;           // 普通用户30秒缓存
const ACTIVE_USER_CACHE_TTL = 10 * 1000; // 活跃用户10秒缓存

// 缓存条目接口
interface CacheEntry {
  data: UserCredits;
  timestamp: number;
  hits: number;                 // 访问次数统计
  version: number;              // 版本号，用于乐观更新
}

// 内存缓存实例
const creditsCache = new Map<string, CacheEntry>();
```

### 缓存统计

```typescript
// 缓存性能统计
let cacheStats = {
  hits: 0,                      // 缓存命中次数
  misses: 0,                    // 缓存未命中次数
  updates: 0                    // 缓存更新次数
};
```

## 积分计算函数

### 图片生成积分计算

```typescript
export function calculateImageGenerationCredits(settings: GenerationSettings): CreditCalculation {
  const baseCredits = CreditsAmount.ImageGenerationBase;
  const qualityMultiplier = settings.highQuality ? 2 : 1;
  const totalCredits = baseCredits * settings.imageCount * qualityMultiplier;

  return {
    totalCredits,
    baseCredits,
    qualityMultiplier,
    imageCount: settings.imageCount,
  };
}
```

### 积分检查函数

```typescript
export async function checkUserCredits(
  userUuid: string, 
  requiredCredits: number
): Promise<{
  sufficient: boolean;
  currentCredits: number;
  requiredCredits: number;
}> {
  const userCredits = await getUserValidCredits(userUuid);
  
  return {
    sufficient: userCredits.totalCredits >= requiredCredits,
    currentCredits: userCredits.totalCredits,
    requiredCredits,
  };
}
```

## 数据库索引优化

### 积分表索引

```sql
-- 用户积分查询优化索引（最重要）
CREATE INDEX IF NOT EXISTS idx_credits_user_expired_credits 
ON credits (user_uuid, expired_at, credits);

-- 用户交易记录查询优化索引
CREATE INDEX IF NOT EXISTS idx_credits_user_created 
ON credits (user_uuid, created_at DESC);

-- 交易号唯一索引
CREATE UNIQUE INDEX IF NOT EXISTS idx_credits_trans_no 
ON credits (trans_no);

-- 订单号查询索引
CREATE INDEX IF NOT EXISTS idx_credits_order_no 
ON credits (order_no) WHERE order_no IS NOT NULL;

-- 过期时间查询索引
CREATE INDEX IF NOT EXISTS idx_credits_expired_at 
ON credits (expired_at) WHERE expired_at IS NOT NULL;

-- 交易类型统计索引
CREATE INDEX IF NOT EXISTS idx_credits_trans_type_created 
ON credits (trans_type, created_at DESC);
```

### 索引性能说明

- **idx_credits_user_expired_credits**: 用于优化用户积分查询，预期将查询时间从200ms降至50ms
- **idx_credits_user_created**: 用于优化用户交易历史查询
- **idx_credits_trans_no**: 确保交易号唯一性
- **idx_credits_order_no**: 用于订单相关积分查询
- **idx_credits_expired_at**: 用于过期积分清理任务
- **idx_credits_trans_type_created**: 用于交易类型统计和报表

## 积分翻译配置

### 中文翻译

```json
{
  "credits": {
    "display": {
      "loading": "...",
      "credits_unit": "积分"
    },
    "generation": {
      "required_credits": "{credits} 积分",
      "insufficient": "积分不足 (当前: {current})",
      "checking": "检查积分...",
      "multiplier_label": "{count}x"
    },
    "insufficient_modal": {
      "title": "积分不足",
      "description": "您的积分不足以完成此次生成，请购买更多积分",
      "current_credits": "当前积分",
      "required_credits": "需要积分",
      "need_more": "还需要",
      "purchase_button": "购买积分",
      "later_button": "稍后再说",
      "tip": "💡 购买积分包可以享受更优惠的价格",
      "close": "关闭"
    },
    "errors": {
      "network_error": "网络连接出现问题，请检查网络后重试",
      "timeout_error": "请求超时，请稍后重试",
      "connection_error": "无法连接到服务器，请检查网络连接",
      "unauthorized": "请先登录后再进行操作",
      "forbidden": "您没有权限执行此操作",
      "session_expired": "登录已过期，请重新登录",
      "insufficient_credits": "积分不足，请购买更多积分",
      "credit_deduction_failed": "积分扣除失败，请稍后重试",
      "credit_refund_failed": "积分退款失败，请联系客服",
      "credit_fetch_failed": "获取积分信息失败，请刷新页面",
      "validation_error": "输入信息有误，请检查后重试",
      "generation_failed": "图片生成失败，请重试",
      "upload_failed": "文件上传失败，请重试",
      "server_error": "服务器出现问题，请稍后重试",
      "database_error": "数据处理出现问题，请稍后重试",
      "service_unavailable": "服务暂时不可用，请稍后重试",
      "unknown_error": "出现未知错误，请稍后重试",
      "retry_button": "重试",
      "refresh_button": "刷新页面",
      "login_button": "重新登录",
      "contact_support": "联系客服",
      "fetch_failed": "获取失败"
    }
  }
}
```

### 英文翻译

```json
{
  "credits": {
    "display": {
      "loading": "...",
      "credits_unit": "credits"
    },
    "generation": {
      "required_credits": "{credits} credits",
      "insufficient": "Insufficient credits (current: {current})",
      "checking": "Checking credits...",
      "multiplier_label": "{count}x"
    },
    "insufficient_modal": {
      "title": "Insufficient Credits",
      "description": "You don't have enough credits to complete this generation. Please purchase more credits.",
      "current_credits": "Current Credits",
      "required_credits": "Required Credits",
      "need_more": "Need More",
      "purchase_button": "Purchase Credits",
      "later_button": "Maybe Later",
      "tip": "💡 Purchase credit packages for better value",
      "close": "Close"
    },
    "errors": {
      "network_error": "Network connection issue, please check your network and try again",
      "timeout_error": "Request timeout, please try again later",
      "connection_error": "Unable to connect to server, please check your network connection",
      "unauthorized": "Please log in first to perform this operation",
      "forbidden": "You don't have permission to perform this operation",
      "session_expired": "Session expired, please log in again",
      "insufficient_credits": "Insufficient credits, please purchase more credits",
      "credit_deduction_failed": "Credit deduction failed, please try again later",
      "credit_refund_failed": "Credit refund failed, please contact support",
      "credit_fetch_failed": "Failed to fetch credit information, please refresh the page",
      "validation_error": "Input information is incorrect, please check and try again",
      "generation_failed": "Image generation failed, please try again",
      "upload_failed": "File upload failed, please try again",
      "server_error": "Server error, please try again later",
      "database_error": "Data processing error, please try again later",
      "service_unavailable": "Service temporarily unavailable, please try again later",
      "unknown_error": "Unknown error occurred, please try again later",
      "retry_button": "Retry",
      "refresh_button": "Refresh Page",
      "login_button": "Log In Again",
      "contact_support": "Contact Support",
      "fetch_failed": "Fetch Failed"
    }
  }
}
```

## 积分套餐配置

### 套餐定义

```json
{
  "pricing": {
    "addon_items": {
      "small": {
        "title": "小量包",
        "description": "适合轻度使用的用户",
        "feature_0": "50 积分",
        "feature_1": "1个月有效期",
        "feature_2": "基础功能支持",
        "feature_3": "邮件客服支持"
      },
      "medium": {
        "title": "中量包",
        "description": "适合中度使用的用户",
        "feature_0": "120 积分",
        "feature_1": "2个月有效期",
        "feature_2": "高级功能支持",
        "feature_3": "优先客服支持",
        "feature_4": "API 访问权限"
      },
      "large": {
        "title": "大量包",
        "description": "适合重度使用的用户",
        "feature_0": "250 积分",
        "feature_1": "3个月有效期",
        "feature_2": "全功能支持",
        "feature_3": "专属客服支持",
        "feature_4": "API 访问权限",
        "feature_5": "优先处理队列"
      }
    }
  }
}
```
