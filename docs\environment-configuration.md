# 环境配置文档

## 概述

本文档详细说明了AI卡通生成器项目的所有环境变量配置和系统设置。

## 环境变量配置

### 基础Web配置

```bash
# 网站基础信息
NEXT_PUBLIC_WEB_URL = "http://localhost:3000"
NEXT_PUBLIC_PROJECT_NAME = "ShipAny"

# 主题配置
NEXT_PUBLIC_DEFAULT_THEME = "light"

# 语言检测
NEXT_PUBLIC_LOCALE_DETECTION = "false"
```

### 数据库配置 (Supabase)

```bash
# Supabase数据库连接
SUPABASE_URL = ""
SUPABASE_ANON_KEY = ""
SUPABASE_SERVICE_ROLE_KEY = ""
```

#### 数据库表结构

##### 用户表 (users)
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    uuid VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VA<PERSON>HA<PERSON>(255),
    avatar_url VARCHAR(255),
    created_at timestamptz,
    updated_at timestamptz,
    status VARCHAR(50)
);
```

##### API密钥表 (apikeys)
```sql
CREATE TABLE apikeys (
    id SERIAL PRIMARY KEY,
    api_key VARCHAR(255) UNIQUE NOT NULL,
    title VARCHAR(100),
    user_uuid VARCHAR(255) NOT NULL,
    created_at timestamptz,
    status VARCHAR(50)
);
```

##### 积分表 (credits)
```sql
CREATE TABLE credits (
    id SERIAL PRIMARY KEY,
    trans_no VARCHAR(255) UNIQUE NOT NULL,
    created_at timestamptz,
    user_uuid VARCHAR(255) NOT NULL,
    trans_type VARCHAR(50) NOT NULL,
    credits INT NOT NULL,
    order_no VARCHAR(255),
    expired_at timestamptz
);
```

##### 文章表 (posts)
```sql
CREATE TABLE posts (
    id SERIAL PRIMARY KEY,
    uuid VARCHAR(255) UNIQUE NOT NULL,
    slug VARCHAR(255),
    title VARCHAR(255),
    description TEXT,
    content TEXT,
    created_at timestamptz,
    updated_at timestamptz,
    status VARCHAR(50),
    cover_url VARCHAR(255),
    author_name VARCHAR(255),
    author_avatar_url VARCHAR(255),
    locale VARCHAR(50)
);
```

### 身份验证配置

#### NextAuth配置
```bash
# Auth密钥 (使用 openssl rand -base64 32 生成)
AUTH_SECRET = "Zt3BXVudzzRq2R2WBqhwRy1dNMq48Gg9zKAYq7YwSL0="
```

#### Google OAuth配置
```bash
# Google OAuth设置
AUTH_GOOGLE_ID = ""
AUTH_GOOGLE_SECRET = ""
NEXT_PUBLIC_AUTH_GOOGLE_ID = ""
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED = "false"
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED = "false"
```

#### GitHub OAuth配置
```bash
# GitHub OAuth设置
AUTH_GITHUB_ID = ""
AUTH_GITHUB_SECRET = ""
NEXT_PUBLIC_AUTH_GITHUB_ENABLED = "false"
```

### 支付系统配置 (Stripe)

```bash
# Stripe API密钥
STRIPE_PUBLIC_KEY = ""
STRIPE_PRIVATE_KEY = ""
STRIPE_WEBHOOK_SECRET = ""

# 支付回调URL
NEXT_PUBLIC_PAY_SUCCESS_URL = "http://localhost:3000/my-orders"
NEXT_PUBLIC_PAY_FAIL_URL = "http://localhost:3000/#pricing"
NEXT_PUBLIC_PAY_CANCEL_URL = "http://localhost:3000/#pricing"
```

### 存储配置 (AWS S3兼容)

```bash
# 对象存储配置
STORAGE_ENDPOINT = ""
STORAGE_REGION = ""
STORAGE_ACCESS_KEY = ""
STORAGE_SECRET_KEY = ""
STORAGE_BUCKET = ""
STORAGE_DOMAIN = ""
```

#### 存储服务配置示例

```typescript
export class Storage {
  private s3: S3Client;

  constructor(config?: StorageConfig) {
    this.s3 = new S3Client({
      endpoint: config?.endpoint || process.env.STORAGE_ENDPOINT || "",
      region: config?.region || process.env.STORAGE_REGION || "auto",
      credentials: {
        accessKeyId: config?.accessKey || process.env.STORAGE_ACCESS_KEY || "",
        secretAccessKey: config?.secretKey || process.env.STORAGE_SECRET_KEY || "",
      },
    });
  }
}
```

### AI服务提供商配置

#### OpenAI配置
```bash
# OpenAI API配置
OPENAI_API_KEY = ""
```

#### Replicate配置
```bash
# Replicate API配置
REPLICATE_API_TOKEN = ""
```

#### Kling AI配置
```bash
# Kling AI API配置
KLING_ACCESS_KEY = ""
KLING_SECRET_KEY = ""
```

### 分析和监控配置

#### Google Analytics
```bash
# Google Analytics配置
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID = ""
```

#### OpenPanel Analytics
```bash
# OpenPanel Analytics配置
NEXT_PUBLIC_OPENPANEL_CLIENT_ID = ""
```

### 管理员配置

```bash
# 管理员邮箱列表 (逗号分隔)
ADMIN_EMAILS = ""
```

## 配置文件

### Next.js配置 (next.config.mjs)

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: "standalone",
  reactStrictMode: false,
  pageExtensions: ["ts", "tsx", "js", "jsx", "md", "mdx"],
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "*",
      },
    ],
  },
  experimental: {
    mdxRs: true,
  },
};
```

### Tailwind配置 (tailwind.config.ts)

```typescript
const config = {
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1200px'
      }
    },
    extend: {
      fontFamily: {
        sans: [
          'var(--font-sans)',
          ...fontFamily.sans
        ]
      },
    }
  }
};
```

### 组件配置 (components.json)

```json
{
  "$schema": "https://ui.shadcn.com/schema.json",
  "style": "default",
  "rsc": true,
  "tsx": true,
  "tailwind": {
    "config": "tailwind.config.ts",
    "css": "app/globals.css",
    "baseColor": "neutral",
    "cssVariables": true,
    "prefix": ""
  },
  "aliases": {
    "components": "@/components",
    "utils": "@/lib/utils",
    "ui": "@/components/ui",
    "lib": "@/lib",
    "hooks": "@/hooks"
  }
}
```

### Vercel配置 (vercel.json)

```json
{
  "functions": {
    "app/api/**/*": {
      "maxDuration": 60
    }
  }
}
```

## 主题配置

### CSS变量配置 (app/theme.css)

```css
:root {
  --background: 0 0% 100%;
  --foreground: 20 14.3% 4.1%;
  --card: 0 0% 100%;
  --card-foreground: 20 14.3% 4.1%;
  --popover: 0 0% 100%;
  --popover-foreground: 20 14.3% 4.1%;
  --primary: 24.6 95% 53.1%;
  --primary-foreground: 60 9.1% 97.8%;
  --secondary: 60 4.8% 95.9%;
  --secondary-foreground: 24 9.8% 10%;
  --muted: 60 4.8% 95.9%;
  --muted-foreground: 25 5.3% 44.7%;
  --accent: 60 4.8% 95.9%;
  --accent-foreground: 24 9.8% 10%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 60 9.1% 97.8%;
  --border: 20 5.9% 90%;
  --input: 20 5.9% 90%;
  --ring: 24.6 95% 53.1%;
  --radius: 0.5rem;
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;
}
```
