import { Credit } from "@/types/credit";
import { getSupabaseClient } from "@/models/db";

export async function insertCredit(credit: Credit) {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase.from("credits").insert(credit);

  if (error) {
    throw error;
  }

  return data;
}

export async function findCreditByTransNo(
  trans_no: string
): Promise<Credit | undefined> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("credits")
    .select("*")
    .eq("trans_no", trans_no)
    .limit(1)
    .single();

  if (error) {
    return undefined;
  }

  return data;
}

export async function getUserValidCredits(
  user_uuid: string
): Promise<Credit[] | undefined> {
  const now = new Date().toISOString();
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("credits")
    .select("*")
    .eq("user_uuid", user_uuid)
    .gte("expired_at", now)
    .order("expired_at", { ascending: true });

  if (error) {
    return undefined;
  }

  return data;
}

/**
 * 批量获取多个用户的有效积分，优化N+1查询问题
 */
export async function getBatchUserValidCredits(
  user_uuids: string[]
): Promise<Map<string, Credit[]>> {
  const result = new Map<string, Credit[]>();

  if (user_uuids.length === 0) {
    return result;
  }

  try {
    const now = new Date().toISOString();
    const supabase = getSupabaseClient();

    // 批量查询所有用户的有效积分
    const { data, error } = await supabase
      .from("credits")
      .select("*")
      .in("user_uuid", user_uuids)
      .gte("expired_at", now)
      .order("expired_at", { ascending: true });

    if (error) {
      return result;
    }

    // 按用户UUID分组
    for (const credit of data || []) {
      const user_uuid = credit.user_uuid;
      if (!result.has(user_uuid)) {
        result.set(user_uuid, []);
      }
      result.get(user_uuid)!.push(credit);
    }

    // 确保所有用户都有记录，即使没有积分
    for (const user_uuid of user_uuids) {
      if (!result.has(user_uuid)) {
        result.set(user_uuid, []);
      }
    }

    return result;
  } catch (e) {
    return result;
  }
}

export async function getCreditsByUserUuid(
  user_uuid: string,
  page: number = 1,
  limit: number = 50
): Promise<Credit[] | undefined> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("credits")
    .select("*")
    .eq("user_uuid", user_uuid)
    .order("created_at", { ascending: false })
    .range((page - 1) * limit, page * limit - 1);

  if (error) {
    return undefined;
  }

  return data;
}
